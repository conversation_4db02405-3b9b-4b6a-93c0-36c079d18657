import { ActivityType, Client } from 'discord.js';

import { BaseEvent } from '@core/BaseEvent';
import { logger } from '@lib/Logger';
import type { ExtendedClient } from '@/client';
import { CommandRegistrar } from '@utils/commandRegistrar';
import { getEnv, isDevelopment } from '@config/environment';

export class ReadyEvent extends BaseEvent<'ready'> {
    public readonly name = 'ready' as const;
    public readonly once = true;

    constructor(client: ExtendedClient) {
        super(client);
    }

    async execute(): Promise<void> {
        const discordClient = this.client as Client;
        if (!discordClient.user) return;

        logger.info(`✅ Bot connected as ${discordClient.user.tag}`);
        logger.info(`📊 Servers: ${discordClient.guilds.cache.size}`);
        logger.info(`👥 Users: ${discordClient.users.cache.size}`);

        // Register commands
        try {
            const env = getEnv();
            logger.info('🔄 Registering slash commands...');

            if (isDevelopment() && env.DISCORD_DEV_GUILD_ID) {
                // In development, register to dev guild
                await CommandRegistrar.registerGuildCommands(this.client, env.DISCORD_DEV_GUILD_ID);
                logger.info('✅ Guild commands registered successfully!');
            } else {
                // In production, register globally
                await CommandRegistrar.registerGlobalCommands(this.client);
                logger.info('✅ Global commands registered successfully!');
            }
        } catch (error) {
            logger.error('❌ Error registering commands:', error);
        }

        // Set bot status
        discordClient.user.setPresence({
            activities: [
                {
                    name: `/help | ${discordClient.guilds.cache.size} servers`,
                    type: ActivityType.Watching,
                },
            ],
            status: 'online',
        });

        // Update status every 5 minutes
        setInterval(() => {
            if (!discordClient.user) return;

            const activities = [
                {
                    name: `/help | ${discordClient.guilds.cache.size} servers`,
                    type: ActivityType.Watching,
                },
                {
                    name: `${discordClient.users.cache.size} users`,
                    type: ActivityType.Listening,
                },
                {
                    name: 'Discord.js v14',
                    type: ActivityType.Playing,
                },
            ];

            const activity = activities[Math.floor(Math.random() * activities.length)];
            if (activity) {
                discordClient.user.setActivity(activity.name, { type: activity.type });
            }
        }, 300000); // 5 minutes
    }
} 