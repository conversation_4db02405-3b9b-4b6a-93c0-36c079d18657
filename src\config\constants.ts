// Couleurs pour les embeds Discord
export const Colors = {
    PRIMARY: 0x5865f2, // Blurple Discord
    SUCCESS: 0x57f287, // Vert
    WARNING: 0xfee75c, // Jaune
    ERROR: 0xed4245, // Rouge
    INFO: 0x5865f2, // Bleu
    NEUTRAL: 0x99aab5, // Gris
} as const;

// Limites de l'application
export const Limits = {
    // Discord
    EMBED_TITLE_LENGTH: 256,
    EMBED_DESCRIPTION_LENGTH: 4096,
    EMBED_FIELD_NAME_LENGTH: 256,
    EMBED_FIELD_VALUE_LENGTH: 1024,
    EMBED_FOOTER_LENGTH: 2048,
    EMBED_AUTHOR_LENGTH: 256,
    EMBED_FIELDS_COUNT: 25,
    MESSAGE_CONTENT_LENGTH: 2000,

    // Bot
    COMMAND_COOLDOWN_DEFAULT: 0, // secondes
    PAGINATION_ITEMS_PER_PAGE: 10,
} as const;

// Emojis personnalisés
export const Emojis = {
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    INFO: 'ℹ️',
    LOADING: '⏳',
    ARROW_RIGHT: '➡️',
    ARROW_LEFT: '⬅️',
    MONEY: '💰',
    TICKET: '🎫',
    FORM: '📝',
    LOCK: '🔒',
    UNLOCK: '🔓',
    STAR: '⭐',
    TROPHY: '🏆',
} as const;

// Permissions par défaut
export const DefaultPermissions = {
    ADMIN_COMMANDS: ['Administrator'],
    MODERATOR_COMMANDS: ['ManageMessages', 'KickMembers', 'BanMembers'],
    TICKET_SUPPORT: ['ManageChannels', 'ManageMessages'],
} as const;

// URLs et liens
export const Links = {
    SUPPORT_SERVER: 'https://discord.gg/your-support-server',
    DOCUMENTATION: 'https://docs.your-bot.com',
    GITHUB: 'https://github.com/your-username/your-bot',
    WEBSITE: 'https://your-bot.com',
} as const;

// Regex patterns
export const Patterns = {
    DISCORD_ID: /^\d{17,19}$/,
    DISCORD_TOKEN: /[\w-]{24}\.[\w-]{6}\.[\w-]{27}/,
    HEX_COLOR: /^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
    URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&\/\/=]*)$/,
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const; 