{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@config/*": ["config/*"], "@core/*": ["core/*"], "@database/*": ["database/*"], "@events/*": ["events/*"], "@lib/*": ["lib/*"], "@modules/*": ["modules/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["@types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}