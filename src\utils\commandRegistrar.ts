import { REST, Routes } from 'discord.js';
import type { RESTPostAPIChatInputApplicationCommandsJSONBody } from 'discord.js';

import { logger } from '@lib/Logger';
import { getEnv } from '@config/environment';
import type { ExtendedClient } from '@/client';

export class CommandRegistrar {
    private static rest: REST;

    /**
     * Initialise le client REST
     */
    private static initRest(): void {
        if (!this.rest) {
            const env = getEnv();
            this.rest = new REST({ version: '10' }).setToken(env.DISCORD_TOKEN);
        }
    }

    /**
     * Enregistre toutes les commandes globalement
     */
    static async registerGlobalCommands(client: ExtendedClient): Promise<void> {
        this.initRest();
        const env = getEnv();

        try {
            const commands: RESTPostAPIChatInputApplicationCommandsJSONBody[] = [];

            // Collecter toutes les commandes
            for (const command of client.commands.values()) {
                commands.push(command.data.toJSON());
            }

            logger.info(`🔄 Registering ${commands.length} global commands...`);

            await this.rest.put(
                Routes.applicationCommands(env.DISCORD_CLIENT_ID),
                { body: commands },
            );

            logger.info(`✅ Successfully registered ${commands.length} global commands`);
        } catch (error) {
            logger.error('❌ Error registering global commands:', error);
            throw error;
        }
    }

    /**
     * Enregistre les commandes pour un serveur de développement
     */
    static async registerGuildCommands(client: ExtendedClient, guildId: string): Promise<void> {
        this.initRest();
        const env = getEnv();

        try {
            const commands: RESTPostAPIChatInputApplicationCommandsJSONBody[] = [];

            // Collecter toutes les commandes
            for (const command of client.commands.values()) {
                commands.push(command.data.toJSON());
            }

            logger.info(`🔄 Registering ${commands.length} commands for guild ${guildId}...`);

            await this.rest.put(
                Routes.applicationGuildCommands(env.DISCORD_CLIENT_ID, guildId),
                { body: commands },
            );

            logger.info(`✅ Successfully registered ${commands.length} commands for guild ${guildId}`);
        } catch (error) {
            logger.error(`❌ Error registering commands for guild ${guildId}:`, error);
            throw error;
        }
    }

    /**
     * Supprime toutes les commandes globales
     */
    static async clearGlobalCommands(): Promise<void> {
        this.initRest();
        const env = getEnv();

        try {
            logger.info('🔄 Clearing all global commands...');

            await this.rest.put(
                Routes.applicationCommands(env.DISCORD_CLIENT_ID),
                { body: [] },
            );

            logger.info('✅ All global commands have been cleared');
        } catch (error) {
            logger.error('❌ Error clearing global commands:', error);
            throw error;
        }
    }

    /**
     * Supprime toutes les commandes d'un serveur
     */
    static async clearGuildCommands(guildId: string): Promise<void> {
        this.initRest();
        const env = getEnv();

        try {
            logger.info(`🔄 Clearing all commands for guild ${guildId}...`);

            await this.rest.put(
                Routes.applicationGuildCommands(env.DISCORD_CLIENT_ID, guildId),
                { body: [] },
            );

            logger.info(`✅ All commands for guild ${guildId} have been cleared`);
        } catch (error) {
            logger.error(`❌ Error clearing commands for guild ${guildId}:`, error);
            throw error;
        }
    }
} 