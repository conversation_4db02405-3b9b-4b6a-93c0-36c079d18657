import type { ClientEvents } from 'discord.js';

import type { ExtendedClient } from '@/client';

export abstract class BaseEvent<K extends keyof ClientEvents> {
    public abstract readonly name: K;
    public abstract readonly once: boolean;
    public readonly client: ExtendedClient;

    constructor(client: ExtendedClient) {
        this.client = client;
    }

    /**
     * Exécute l'événement
     */
    abstract execute(...args: ClientEvents[K]): void | Promise<void>;

    /**
     * Enregistre l'événement sur le client
     */
    public register(): void {
        if (this.once) {
            this.client.once(this.name, this.execute.bind(this));
        } else {
            this.client.on(this.name, this.execute.bind(this));
        }
    }

    /**
     * Désenregistre l'événement du client
     */
    public unregister(): void {
        this.client.removeListener(this.name, this.execute.bind(this));
    }
} 