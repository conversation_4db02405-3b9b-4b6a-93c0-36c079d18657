import { SlashCommandBuilder, Client } from 'discord.js';
import type { ChatInputCommandInteraction } from 'discord.js';

import { BaseCommand } from '@core/BaseCommand';
import type { ExtendedClient } from '@/client';

export class PingCommand extends BaseCommand {
    constructor(client: ExtendedClient) {
        super(client, {
            category: 'GENERAL',
            nameKey: 'commands:ping.name',
            descriptionKey: 'commands:ping.description',
            cooldown: 0,
        });
    }

    buildCommand(): SlashCommandBuilder {
        // Retourner simplement un builder vide, les traductions seront appliquées automatiquement
        return new SlashCommandBuilder();
    }

    async execute(interaction: ChatInputCommandInteraction): Promise<void> {
        // Envoyer un message initial
        const pingingMsg = await this.translate('commands:ping.responses.pinging', interaction);

        const sent = await interaction.reply({
            content: pingingMsg,
            fetchReply: true,
        });

        // Calculer la latence
        const latency = sent.createdTimestamp - interaction.createdTimestamp;
        const discordClient = this.client as Client;
        const apiLatency = Math.round(discordClient.ws.ping);

        // Envoyer le résultat
        const resultMsg = await this.translate('commands:ping.responses.result', interaction, {
            latency,
            apiLatency,
        });

        await interaction.editReply({
            content: resultMsg,
        });
    }
} 