import { BasePlugin } from '@core/BasePlugin';
import type { ExtendedClient } from '@/client';
import { ClusterStatsCommand } from './commands/clusterstats.command';

export default class AdminPlugin extends BasePlugin {
    public readonly info = {
        name: 'Admin',
        version: '1.0.0',
        description: 'Administration commands and tools',
        author: 'Xink0',
    };

    constructor(client: ExtendedClient) {
        super(client);
    }

    protected async onLoad(): Promise<void> {
        // Charger les commandes d'administration
        this.addCommand(new ClusterStatsCommand(this.client));

        // TODO: Ajouter d'autres commandes d'administration
        // this.addCommand(new RestartCommand(this.client));
        // this.addCommand(new StatusCommand(this.client));
        // this.addCommand(new ConfigCommand(this.client));
    }

    protected async onUnload(): Promise<void> {
        // Nettoyer les ressources si nécessaire
    }
} 