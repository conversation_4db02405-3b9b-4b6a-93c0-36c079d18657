import winston from 'winston';

import { getEnv, isDevelopment } from '@config/environment';

const { combine, timestamp, printf, colorize, errors } = winston.format;

// Format personnalisé pour les logs
const logFormat = printf(({ level, message, timestamp, stack }) => {
    const log = `${timestamp} [${level}]: ${message}`;
    return stack ? `${log}\n${stack}` : log;
});

// Configuration du logger
export const logger = winston.createLogger({
    level: getEnv()?.LOG_LEVEL || 'info',
    format: combine(
        errors({ stack: true }),
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat,
    ),
    transports: [
        // Console (colorée en développement)
        new winston.transports.Console({
            format: isDevelopment()
                ? combine(
                    colorize({ all: true }),
                    errors({ stack: true }),
                    timestamp({ format: 'HH:mm:ss' }),
                    logFormat,
                )
                : undefined,
        }),
        // Fichier pour les erreurs
        new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Fichier pour tous les logs
        new winston.transports.File({
            filename: 'logs/combined.log',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
    exceptionHandlers: [
        new winston.transports.File({ filename: 'logs/exceptions.log' }),
    ],
    rejectionHandlers: [
        new winston.transports.File({ filename: 'logs/rejections.log' }),
    ],
});

// En développement, ajouter aussi les logs dans des fichiers séparés par niveau
if (isDevelopment()) {
    logger.add(
        new winston.transports.File({
            filename: 'logs/debug.log',
            level: 'debug',
        }),
    );
}

// Créer un logger enfant pour des contextes spécifiques
export function createLogger(context: string) {
    return logger.child({ context });
} 