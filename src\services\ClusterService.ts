import { ClusterClient } from 'discord-hybrid-sharding';
import type { ExtendedClient } from '@/client';
import { logger } from '@lib/Logger';

export class ClusterService {
    private static instance: ClusterService;
    private client: ExtendedClient | null = null;
    private clusterClient: ClusterClient | null = null;

    private constructor() { }

    public static getInstance(): ClusterService {
        if (!ClusterService.instance) {
            ClusterService.instance = new ClusterService();
        }
        return ClusterService.instance;
    }

    /**
     * Initialize the cluster service with the Discord client
     */
    public initialize(client: ExtendedClient): void {
        this.client = client;
        this.clusterClient = client.cluster || null;

        if (this.clusterClient) {
            logger.info(`🔷 Cluster service initialized for cluster ${this.getClusterId()}`);
        } else {
            logger.info('🔷 Cluster service initialized in single process mode');
        }
    }

    /**
     * Check if clustering is enabled
     */
    public isClustered(): boolean {
        return this.clusterClient !== null;
    }

    /**
     * Get the current cluster ID
     */
    public getClusterId(): number | null {
        return this.clusterClient?.id ?? null;
    }

    /**
     * Get cluster information
     */
    public getClusterInfo(): any {
        if (!this.clusterClient) return null;

        return {
            id: this.clusterClient.id,
            shardList: this.clusterClient.info.SHARD_LIST,
            totalShards: this.clusterClient.info.TOTAL_SHARDS,
            totalClusters: this.clusterClient.info.CLUSTER_COUNT,
        };
    }

    /**
     * Broadcast evaluation to all clusters
     */
    public async broadcastEval<T>(script: string): Promise<T[]> {
        if (!this.clusterClient) {
            throw new Error('Clustering is not enabled');
        }

        try {
            return await this.clusterClient.broadcastEval(script);
        } catch (error) {
            logger.error('❌ Error broadcasting eval:', error);
            throw error;
        }
    }

    /**
     * Send data to a specific cluster
     */
    public async sendToCluster(clusterId: number, data: any): Promise<any> {
        if (!this.clusterClient) {
            throw new Error('Clustering is not enabled');
        }

        try {
            return await this.clusterClient.send({ clusterId, data });
        } catch (error) {
            logger.error(`❌ Error sending data to cluster ${clusterId}:`, error);
            throw error;
        }
    }

    /**
     * Request data from a specific cluster
     */
    public async requestFromCluster<T>(clusterId: number, data: any): Promise<T> {
        if (!this.clusterClient) {
            throw new Error('Clustering is not enabled');
        }

        try {
            return await this.clusterClient.request({ clusterId, data }) as T;
        } catch (error) {
            logger.error(`❌ Error requesting data from cluster ${clusterId}:`, error);
            throw error;
        }
    }

    /**
     * Get total guild count across all clusters
     */
    public async getTotalGuildCount(): Promise<number> {
        if (!this.clusterClient) {
            return this.client?.guilds.cache.size ?? 0;
        }

        try {
            const results = await this.broadcastEval<number>('this.guilds.cache.size');
            return results.reduce((acc: number, count: number) => acc + count, 0);
        } catch (error) {
            logger.error('❌ Error getting total guild count:', error);
            return 0;
        }
    }

    /**
     * Get total user count across all clusters
     */
    public async getTotalUserCount(): Promise<number> {
        if (!this.clusterClient) {
            return this.client?.users.cache.size ?? 0;
        }

        try {
            const results = await this.broadcastEval<number>('this.users.cache.size');
            return results.reduce((acc: number, count: number) => acc + count, 0);
        } catch (error) {
            logger.error('❌ Error getting total user count:', error);
            return 0;
        }
    }

    /**
     * Get cluster statistics
     */
    public async getClusterStats(): Promise<any[]> {
        if (!this.clusterClient) {
            return [{
                clusterId: 0,
                ping: this.client?.ws.ping ?? 0,
                guilds: this.client?.guilds.cache.size ?? 0,
                users: this.client?.users.cache.size ?? 0,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
            }];
        }

        try {
            const results = await this.broadcastEval(`({
                clusterId: this.cluster?.id ?? 0,
                ping: this.ws.ping,
                guilds: this.guilds.cache.size,
                users: this.users.cache.size,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
            })`);

            return results;
        } catch (error) {
            logger.error('❌ Error getting cluster stats:', error);
            return [];
        }
    }

    /**
     * Restart a specific cluster
     */
    public async restartCluster(clusterId: number): Promise<void> {
        if (!this.clusterClient) {
            throw new Error('Clustering is not enabled');
        }

        try {
            await this.clusterClient.send({
                clusterId,
                data: { action: 'restart' }
            });
            logger.info(`🔄 Restart signal sent to cluster ${clusterId}`);
        } catch (error) {
            logger.error(`❌ Error restarting cluster ${clusterId}:`, error);
            throw error;
        }
    }

    /**
     * Setup cluster message handlers
     */
    public setupMessageHandlers(): void {
        if (!this.clusterClient) return;

        this.clusterClient.on('message', (message: any) => {
            logger.debug(`📨 Received cluster message:`, message);

            // Handle different message types
            if (typeof message === 'object' && message?.data?.action === 'restart') {
                logger.info('🔄 Received restart signal, restarting cluster...');
                process.exit(0);
            }

            // Add more message handlers as needed
        });

        logger.info('✅ Cluster message handlers setup complete');
    }

    /**
     * Get shard information for this cluster
     */
    public getShardInfo(): any {
        if (!this.client) return null;

        return {
            shardId: this.client.shard?.ids ?? [0],
            shardCount: this.client.shard?.count ?? 1,
            ping: this.client.ws.ping,
            status: this.client.ws.status,
        };
    }
} 