import { z } from 'zod';

// Schéma de validation pour les variables d'environnement
const envSchema = z.object({
    // Discord
    DISCORD_TOKEN: z.string().min(1),
    DISCORD_CLIENT_ID: z.string().min(1),
    DISCORD_DEV_GUILD_ID: z.string().optional(),

    // Database
    DATABASE_URL: z.string().url().or(z.string().startsWith('mysql://')),

    // Redis
    REDIS_URL: z.string().default('redis://localhost:6379'),
    REDIS_PASSWORD: z.string().optional(),
    ENABLE_REDIS: z.string().transform(val => val === 'true').default('false'),

    // API
    API_PORT: z.string().regex(/^\d+$/).transform(Number).default('3000'),
    API_SECRET: z.string().min(32).optional(),

    // Payment
    STRIPE_SECRET_KEY: z.string().optional(),
    STRIPE_WEBHOOK_SECRET: z.string().optional(),
    CUSTOM_PAYMENT_API_KEY: z.string().optional(),
    CUSTOM_PAYMENT_API_URL: z.string().url().optional(),

    // Environment
    NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

    // Logging
    LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    LOG_DIR: z.string().default('./logs'),

    // Bot Settings
    DEFAULT_PREFIX: z.string().default('!'),
    DEFAULT_LANGUAGE: z.string().default('fr'),
    BOT_OWNER_ID: z.string().optional(),

    // Feature Flags
    ENABLE_PAYMENTS: z.string().transform(val => val === 'true').default('true'),
    ENABLE_XP_SYSTEM: z.string().transform(val => val === 'true').default('true'),
    ENABLE_TICKETS: z.string().transform(val => val === 'true').default('true'),
    ENABLE_FORMS: z.string().transform(val => val === 'true').default('true'),

    // External APIs
    YOUTUBE_API_KEY: z.string().optional(),
    TWITCH_CLIENT_ID: z.string().optional(),
    TWITCH_CLIENT_SECRET: z.string().optional(),

    // Webhooks
    WEBHOOK_PORT: z.string().regex(/^\d+$/).transform(Number).default('3001'),
    WEBHOOK_SECRET: z.string().min(32).optional(),

    // Sharding
    USE_SHARDING: z.string().transform(val => val === 'true').default('false'),

    // Clustering (discord-hybrid-sharding)
    USE_CLUSTERING: z.string().transform(val => val === 'true').default('false'),
    CLUSTERS_COUNT: z.string().regex(/^\d+$/).transform(Number).default('1'),
    SHARDS_PER_CLUSTER: z.string().regex(/^\d+$/).transform(Number).default('2'),
    CLUSTER_RESPAWN: z.string().transform(val => val === 'true').default('true'),
    CLUSTER_RESPAWN_DELAY: z.string().regex(/^\d+$/).transform(Number).default('5000'),
});

// Type pour l'environnement validé
export type Environment = z.infer<typeof envSchema>;

// Variable globale pour stocker l'environnement validé
let env: Environment;

/**
 * Valide et charge les variables d'environnement
 */
export function validateEnvironment(): Environment {
    try {
        env = envSchema.parse(process.env);
        return env;
    } catch (error) {
        if (error instanceof z.ZodError) {
            console.error('❌ Erreur de validation des variables d\'environnement:');
            error.errors.forEach((err) => {
                console.error(`  - ${err.path.join('.')}: ${err.message}`);
            });
            process.exit(1);
        }
        throw error;
    }
}

/**
 * Récupère l'environnement validé
 */
export function getEnv(): Environment {
    if (!env) {
        throw new Error('L\'environnement n\'a pas été validé. Appelez validateEnvironment() d\'abord.');
    }
    return env;
}

/**
 * Vérifie si on est en mode développement
 */
export function isDevelopment(): boolean {
    return getEnv().NODE_ENV === 'development';
}

/**
 * Vérifie si on est en mode production
 */
export function isProduction(): boolean {
    return getEnv().NODE_ENV === 'production';
} 