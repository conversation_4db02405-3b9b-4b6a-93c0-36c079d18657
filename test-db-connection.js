const { PrismaClient } = require('@prisma/client');

async function testConnection() {
    const prisma = new PrismaClient();
    
    try {
        console.log('🔍 Testing database connection...');
        
        // Test basic connection
        await prisma.$connect();
        console.log('✅ Database connected successfully');
        
        // Test if tables exist by running a simple query
        const result = await prisma.$queryRaw`SHOW TABLES`;
        console.log('📋 Tables in database:', result);
        
        // Test Guild table
        const guildCount = await prisma.guild.count();
        console.log(`📊 Guild table exists with ${guildCount} records`);
        
        // Test User table
        const userCount = await prisma.user.count();
        console.log(`👥 User table exists with ${userCount} records`);
        
        console.log('🎉 All tests passed! Your database is working correctly.');
        
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
    } finally {
        await prisma.$disconnect();
    }
}

testConnection();
