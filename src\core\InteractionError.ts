export class InteractionError extends Error {
    public readonly code: string;
    public readonly ephemeral: boolean;

    constructor(message: string, code = 'INTERACTION_ERROR', ephemeral = true) {
        super(message);
        this.name = 'InteractionError';
        this.code = code;
        this.ephemeral = ephemeral;
    }

    /**
     * Crée une erreur de permission
     */
    static permission(message = 'Vous n\'avez pas la permission d\'effectuer cette action.'): InteractionError {
        return new InteractionError(message, 'PERMISSION_ERROR');
    }

    /**
     * Crée une erreur de cooldown
     */
    static cooldown(seconds: number): InteractionError {
        return new InteractionError(
            `Veuillez attendre ${seconds} secondes avant de réutiliser cette commande.`,
            'COOLDOWN_ERROR',
        );
    }

    /**
     * Crée une erreur de validation
     */
    static validation(message: string): InteractionError {
        return new InteractionError(message, 'VALIDATION_ERROR');
    }

    /**
     * Crée une erreur de ressource non trouvée
     */
    static notFound(resource: string): InteractionError {
        return new InteractionError(`${resource} introuvable.`, 'NOT_FOUND_ERROR');
    }

    /**
     * Crée une erreur de limite atteinte
     */
    static limitReached(resource: string, limit: number): InteractionError {
        return new InteractionError(
            `Vous avez atteint la limite de ${limit} ${resource}.`,
            'LIMIT_ERROR',
        );
    }

    /**
     * Crée une erreur de serveur
     */
    static serverError(message = 'Une erreur serveur s\'est produite. Veuillez réessayer plus tard.'): InteractionError {
        return new InteractionError(message, 'SERVER_ERROR');
    }
} 