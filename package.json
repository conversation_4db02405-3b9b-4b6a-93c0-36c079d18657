{"name": "xinko-bot", "version": "1.0.0", "description": "<PERSON><PERSON>", "main": "dist/app.js", "_moduleAliases": {"@": "dist", "@config": "dist/config", "@core": "dist/core", "@database": "dist/database", "@events": "dist/events", "@lib": "dist/lib", "@modules": "dist/modules", "@services": "dist/services", "@utils": "dist/utils", "@types": "dist/@types"}, "scripts": {"start": "node dist/app.js", "dev": "concurrently \"tsc --watch\" \"nodemon dist/app.js\"", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "discord-hybrid-sharding": "^2.2.6", "discord.js": "^14.19.3", "dotenv": "^16.5.0", "express": "^5.1.0", "i18next": "^25.2.1", "i18next-fs-backend": "^2.6.0", "ioredis": "^5.6.1", "module-alias": "^2.2.3", "mysql2": "^3.14.1", "stripe": "^18.2.0", "typescript": "^5.8.3", "winston": "^3.17.0", "xinko-bot": "file:", "zod": "^3.25.46"}, "devDependencies": {"@types/express": "^5.0.2", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.12", "@types/node": "^22.15.29", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "concurrently": "^9.1.2", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "^3.3.3", "prisma": "^6.8.2", "ts-jest": "^29.2.0", "ts-node": "^10.9.2"}, "engines": {"node": ">=18.0.0"}, "private": true}