require('module-alias/register');
import 'dotenv/config';
import { validateEnvironment, getEnv } from '@config/environment';

// Valider l'environnement dès que possible
validateEnvironment();

import { createClient } from '@/client';
import { PrismaService } from '@database/prisma.service';
import { RedisService } from '@database/redis.service';
import { logger } from '@lib/Logger';
import { loadModules } from '@modules/index';
import { I18nService } from '@services/I18nService';
import { ClusterService } from '@services/ClusterService';

async function startBot() {
    try {
        logger.info('🚀 Starting Discord bot...');
        logger.info('📦 Initializing services...');

        // Get validated environment
        const env = getEnv();

        // Database
        await PrismaService.connect();

        // Redis (optional)
        if (env.ENABLE_REDIS) {
            await RedisService.connect();
        } else {
            logger.info('⚠️ Redis disabled in configuration');
            RedisService.disable();
        }

        // Internationalization
        await I18nService.initialize();

        // Create Discord client
        const client = createClient();

        // Initialize cluster service
        const clusterService = ClusterService.getInstance();
        clusterService.initialize(client);
        clusterService.setupMessageHandlers();

        // Log cluster information
        if (clusterService.isClustered()) {
            const clusterInfo = clusterService.getClusterInfo();
            logger.info(`🔷 Running on cluster ${clusterInfo.id} with shards [${clusterInfo.shardList.join(', ')}]`);
            logger.info(`📊 Total clusters: ${clusterInfo.totalClusters}, Total shards: ${clusterInfo.totalShards}`);
        }

        // Load modules
        await loadModules(client);
        logger.info('✅ Modules loaded');

        // Connect to Discord
        logger.info('🔗 Connecting to Discord...');
        await client.login(env.DISCORD_TOKEN);
    } catch (error) {
        logger.error('❌ Fatal error during bot startup:', error);
        throw error;
    }
}

// Graceful shutdown handling
async function gracefulShutdown() {
    logger.info('🛑 Shutting down bot...');

    await PrismaService.disconnect();

    const env = getEnv();
    if (env.ENABLE_REDIS) {
        await RedisService.disconnect();
    }

    process.exit(0);
}

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Start the bot
startBot(); 