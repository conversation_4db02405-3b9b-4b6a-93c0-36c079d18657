# Node modules
node_modules/

# Environment variables
.env
.env.local
.env.development
.env.production

# Build output
dist/
build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/

# Cache
.cache/
.npm/
.eslintcache
.tsbuildinfo

# Database
*.sqlite
*.sqlite3
*.db

# Prisma
prisma/migrations/dev/

# TypeScript
*.tsbuildinfo

# Package manager
yarn.lock
package-lock.json
pnpm-lock.yaml

# Bot specific
config.json
tokens.json 