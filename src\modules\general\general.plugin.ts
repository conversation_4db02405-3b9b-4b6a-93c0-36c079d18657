import { BasePlugin } from '@core/BasePlugin';
import type { ExtendedClient } from '@/client';
import { PingCommand } from './commands/ping.command';
import { ReadyEvent } from '@events/ready';
import { InteractionCreateEvent } from '@events/interactionCreate';

export default class GeneralPlugin extends BasePlugin {
    public readonly info = {
        name: 'General',
        version: '1.0.0',
        description: 'Core functionality and general commands',
        author: 'Xink0',
    };

    constructor(client: ExtendedClient) {
        super(client);
    }

    protected async onLoad(): Promise<void> {
        // Charger les événements globaux
        this.addEvent(new ReadyEvent(this.client));
        this.addEvent(new InteractionCreateEvent(this.client));

        // Charger les commandes générales
        this.addCommand(new PingCommand(this.client));

        // TODO: Ajouter d'autres commandes générales (help, info, etc.)
    }

    protected async onUnload(): Promise<void> {
        // Nettoyer les ressources si nécessaire
    }
} 