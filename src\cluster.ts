require('module-alias/register');
import 'dotenv/config';
import { validateEnvironment, getEnv } from '@config/environment';

// Valider l'environnement dès que possible
validateEnvironment();

import { ClusterManager, HeartbeatManager, ReClusterManager } from 'discord-hybrid-sharding';
import { resolve } from 'path';

import { logger } from '@lib/Logger';

async function startClusterManager() {
    try {
        // Get validated environment
        const env = getEnv();

        logger.info('🚀 Starting Discord bot with clustering...');
        logger.info(`📊 Configuration: ${env.CLUSTERS_COUNT} clusters, ${env.SHARDS_PER_CLUSTER} shards per cluster`);

        // Create cluster manager
        const manager = new ClusterManager(resolve(__dirname, 'bot.js'), {
            totalShards: 'auto', // or specify a number
            totalClusters: env.CLUSTERS_COUNT,
            shardsPerClusters: env.SHARDS_PER_CLUSTER,
            mode: 'process', // or 'worker'
            token: env.DISCORD_TOKEN,
            respawn: env.CLUSTER_RESPAWN,
            restarts: {
                max: 5,
                interval: 60000 * 60, // 1 hour
            },
        });

        // Setup heartbeat manager for cluster health monitoring
        const heartbeat = new HeartbeatManager({
            interval: 30000, // 30 seconds
            maxMissedHeartbeats: 5,
        });

        // Setup re-cluster manager for automatic re-clustering
        const recluster = new ReClusterManager({
            restartMode: 'gracefulSwitch',
            delay: env.CLUSTER_RESPAWN_DELAY,
        });

        // Event listeners for cluster manager
        manager.on('clusterCreate', (cluster) => {
            logger.info(`🔷 Cluster ${cluster.id} created`);

            cluster.on('spawn', () => {
                logger.info(`✅ Cluster ${cluster.id} spawned`);
            });

            cluster.on('ready', () => {
                logger.info(`🟢 Cluster ${cluster.id} ready`);
            });

            cluster.on('death', () => {
                logger.warn(`💀 Cluster ${cluster.id} died`);
            });

            cluster.on('disconnect', () => {
                logger.warn(`🔌 Cluster ${cluster.id} disconnected`);
            });

            cluster.on('reconnecting', () => {
                logger.info(`🔄 Cluster ${cluster.id} reconnecting`);
            });

            cluster.on('message', (message) => {
                logger.debug(`📨 Message from cluster ${cluster.id}:`, message);
            });

            cluster.on('error', (error) => {
                logger.error(`❌ Error in cluster ${cluster.id}:`, error);
            });
        });

        // Global manager events
        manager.on('debug', (message) => {
            logger.debug(`🐛 Cluster Manager Debug: ${message}`);
        });

        // Extend manager with heartbeat and recluster functionality
        manager.extend(heartbeat);
        manager.extend(recluster);

        // Start spawning clusters
        await manager.spawn({ timeout: 60000 });
        logger.info('✅ All clusters have been spawned');

        // Setup graceful shutdown
        const gracefulShutdown = async () => {
            logger.info('🛑 Shutting down cluster manager...');

            try {
                // Broadcast shutdown message to all clusters
                await manager.broadcastEval('process.exit(0)');

                // Wait a bit for clusters to shutdown gracefully
                await new Promise(resolve => setTimeout(resolve, 5000));

                process.exit(0);
            } catch (error) {
                logger.error('❌ Error during graceful shutdown:', error);
                process.exit(1);
            }
        };

        process.on('SIGINT', gracefulShutdown);
        process.on('SIGTERM', gracefulShutdown);

        // Setup cluster communication examples
        setupClusterCommunication(manager);

    } catch (error) {
        logger.error('❌ Fatal error during cluster manager startup:', error);
        process.exit(1);
    }
}

function setupClusterCommunication(manager: ClusterManager) {
    // Example: Get total guild count across all clusters
    setInterval(async () => {
        try {
            const results = await manager.broadcastEval('this.guilds.cache.size');
            const totalGuilds = results.reduce((acc, guildCount) => acc + guildCount, 0);
            logger.info(`📊 Total guilds across all clusters: ${totalGuilds}`);
        } catch (error) {
            logger.error('❌ Error getting guild count:', error);
        }
    }, 300000); // Every 5 minutes

    // Example: Get cluster statistics
    setInterval(async () => {
        try {
            const clusterStats = await manager.fetchClientValues('ws.ping');
            clusterStats.forEach((ping: any, index: number) => {
                logger.debug(`🏓 Cluster ${index} ping: ${ping}ms`);
            });
        } catch (error) {
            logger.error('❌ Error getting cluster stats:', error);
        }
    }, 60000); // Every minute
}

// Handle uncaught errors
process.on('unhandledRejection', (error: Error) => {
    logger.error('Unhandled Rejection in Cluster Manager:', error);
});

process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception in Cluster Manager:', error);
    process.exit(1);
});

// Start the cluster manager
startClusterManager(); 