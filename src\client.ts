import {
    Client,
    Collection,
    GatewayIntentBits,
    Partials,
    type ClientOptions,
} from 'discord.js';
import { ClusterClient, getInfo } from 'discord-hybrid-sharding';

import type { BaseCommand } from '@core/BaseCommand';
import type { BasePlugin } from '@core/BasePlugin';
import { getEnv } from '@config/environment';

// Extension du client Discord avec nos propriétés personnalisées
export interface ExtendedClient extends Client {
    commands: Collection<string, BaseCommand>;
    plugins: Collection<string, BasePlugin>;
    cluster?: ClusterClient;
}

export function createClient(): ExtendedClient {
    const env = getEnv();

    const clientOptions: ClientOptions = {
        intents: [
            GatewayIntentBits.Guilds,
            GatewayIntentBits.GuildMembers,
            GatewayIntentBits.GuildModeration,
            GatewayIntentBits.GuildEmojisAndStickers,
            GatewayIntentBits.GuildIntegrations,
            GatewayIntentBits.GuildWebhooks,
            GatewayIntentBits.GuildInvites,
            GatewayIntentBits.GuildVoiceStates,
            GatewayIntentBits.GuildPresences,
            GatewayIntentBits.GuildMessages,
            GatewayIntentBits.GuildMessageReactions,
            GatewayIntentBits.GuildMessageTyping,
            GatewayIntentBits.DirectMessages,
            GatewayIntentBits.DirectMessageReactions,
            GatewayIntentBits.DirectMessageTyping,
            GatewayIntentBits.MessageContent,
            GatewayIntentBits.GuildScheduledEvents,
            GatewayIntentBits.AutoModerationConfiguration,
            GatewayIntentBits.AutoModerationExecution,
        ],
        partials: [
            Partials.Channel,
            Partials.Message,
            Partials.User,
            Partials.GuildMember,
            Partials.Reaction,
            Partials.GuildScheduledEvent,
            Partials.ThreadMember,
        ],
        allowedMentions: {
            parse: ['users', 'roles'],
            repliedUser: false,
        },
        rest: {
            timeout: 30000,
            retries: 3,
        },
    };

    // Only add sharding configuration if clustering is enabled
    if (env.USE_CLUSTERING) {
        try {
            const info = getInfo();
            clientOptions.shards = info.SHARD_LIST;
            clientOptions.shardCount = info.TOTAL_SHARDS;
        } catch (error) {
            // If getInfo() fails, we're not in a cluster environment
            console.warn('⚠️ Clustering is enabled but not running in cluster environment');
        }
    }

    const client = new Client(clientOptions) as ExtendedClient;

    // Initialiser les collections personnalisées
    client.commands = new Collection();
    client.plugins = new Collection();

    // Add cluster client only if clustering is enabled and we're in a cluster environment
    if (env.USE_CLUSTERING) {
        try {
            client.cluster = new ClusterClient(client);
        } catch (error) {
            // If ClusterClient creation fails, we're not in a cluster environment
            console.warn('⚠️ Could not create ClusterClient, running in single process mode');
        }
    }

    return client;
} 