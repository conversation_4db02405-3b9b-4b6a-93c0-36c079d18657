import i18next from 'i18next';
import Backend from 'i18next-fs-backend';
import { join } from 'path';

import { logger } from '@lib/Logger';
import { getEnv } from '@config/environment';
import type { Guild, User } from 'discord.js';
import { PrismaService } from '@database/prisma.service';

export class I18nService {
    private static initialized = false;
    private static instance: typeof i18next | null = null;
    private static defaultLanguage = 'en-US';
    private static supportedLanguages = ['en-US', 'fr-FR'];

    /**
     * Initializes the i18n service
     */
    static async initialize(): Promise<void> {
        if (this.initialized) {
            logger.debug('I18n already initialized, skipping');
            return;
        }

        const env = getEnv();
        this.defaultLanguage = env.DEFAULT_LANGUAGE || 'en-US';

        await i18next
            .use(Backend)
            .init({
                lng: this.defaultLanguage,
                fallbackLng: 'en-US',
                supportedLngs: this.supportedLanguages,
                ns: ['common', 'commands', 'categories', 'errors', 'success'],
                defaultNS: 'common',
                backend: {
                    loadPath: join(process.cwd(), 'locales/{{lng}}/{{ns}}.json'),
                },
                interpolation: {
                    escapeValue: false,
                },
                debug: false,
                initImmediate: false,
                saveMissing: false,
                saveMissingTo: 'current',
            });

        this.instance = i18next;
        this.initialized = true;
        logger.info('✅ I18n service initialized');
    }

    /**
     * Gets the preferred language for a user or guild
     */
    static async getLanguage(user?: User, guild?: Guild): Promise<string> {
        // Priority: User > Guild > Default
        if (user) {
            const prisma = PrismaService.getInstance();
            const userData = await prisma.user.findUnique({
                where: { discordId: user.id },
                select: { preferredLanguage: true },
            }).catch(() => null);

            if (userData?.preferredLanguage) {
                return userData.preferredLanguage;
            }
        }

        if (guild) {
            const prisma = PrismaService.getInstance();
            const guildData = await prisma.guild.findUnique({
                where: { guildId: guild.id },
                select: { language: true },
            }).catch(() => null);

            if (guildData?.language) {
                return guildData.language;
            }
        }

        return this.defaultLanguage;
    }

    /**
     * Translates a key
     */
    static t(key: string, options?: any, lng?: string): string {
        if (!this.instance) {
            logger.warn('I18n not initialized, returning key:', key);
            return key;
        }
        const result = this.instance.t(key, { lng, ...options });
        return typeof result === 'string' ? result : key;
    }

    /**
     * Translates a key with user/guild context
     */
    static async translate(
        key: string,
        context: { user?: User; guild?: Guild },
        options?: any,
    ): Promise<string> {
        const lng = await this.getLanguage(context.user, context.guild);
        return this.t(key, options, lng);
    }

    /**
     * Gets all translations for a namespace
     */
    static getNamespace(namespace: string, lng?: string): any {
        if (!this.instance) return {};
        return this.instance.getResourceBundle(lng || this.defaultLanguage, namespace);
    }

    /**
     * Checks if a language is supported
     */
    static isSupported(language: string): boolean {
        return this.supportedLanguages.includes(language);
    }

    /**
     * Gets the list of supported languages
     */
    static getSupportedLanguages(): string[] {
        return [...this.supportedLanguages];
    }

    /**
     * Changes the default language
     */
    static async changeLanguage(language: string): Promise<void> {
        if (!this.isSupported(language)) {
            throw new Error(`Unsupported language: ${language}`);
        }

        await i18next.changeLanguage(language);
    }
} 