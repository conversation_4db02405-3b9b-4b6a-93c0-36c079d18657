// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Configuration par serveur Discord
model Guild {
  id              String    @id @default(cuid())
  guildId         String    @unique
  language        String    @default("fr")

  modulesEnabled  Json      @default("{}")
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@index([guildId])
}

// Utilisateurs
model User {
  id              String    @id @default(cuid())
  discordId       String    @unique
  preferredLanguage String  @default("en-US")
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@index([discordId])
}