import type {
    ButtonInteraction,
    ModalSubmitInteraction,
    StringSelectMenuInteraction,
    UserSelectMenuInteraction,
    RoleSelectMenuInteraction,
    ChannelSelectMenuInteraction,
    MentionableSelectMenuInteraction,
} from 'discord.js';

import type { ExtendedClient } from '@/client';

export type InteractionHandler =
    | ButtonInteraction
    | ModalSubmitInteraction
    | StringSelectMenuInteraction
    | UserSelectMenuInteraction
    | RoleSelectMenuInteraction
    | ChannelSelectMenuInteraction
    | MentionableSelectMenuInteraction;

export abstract class BaseInteraction<T extends InteractionHandler = InteractionHandler> {
    public abstract readonly customId: string | RegExp;
    public readonly client: ExtendedClient;

    constructor(client: ExtendedClient) {
        this.client = client;
    }

    /**
     * Exécute l'interaction
     */
    abstract execute(interaction: T): Promise<void>;

    /**
     * Vérifie si cette interaction correspond au customId
     */
    public matches(customId: string): boolean {
        if (typeof this.customId === 'string') {
            return this.customId === customId;
        }
        return this.customId.test(customId);
    }

    /**
     * Méthode utilitaire pour répondre avec une erreur
     */
    protected async replyError(interaction: T, message: string): Promise<void> {
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({
                content: message,
            });
        } else {
            await interaction.reply({
                content: message,
                ephemeral: true,
            });
        }
    }

    /**
     * Méthode utilitaire pour répondre avec un succès
     */
    protected async replySuccess(interaction: T, message: string): Promise<void> {
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({
                content: message,
            });
        } else {
            await interaction.reply({
                content: message,
                ephemeral: false,
            });
        }
    }
} 