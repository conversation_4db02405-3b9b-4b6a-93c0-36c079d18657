import { PrismaClient } from '@prisma/client';

import { logger } from '@lib/Logger';

class PrismaService {
    private static instance: PrismaClient | null = null;
    private static connected = false;

    /**
     * Gets the Prisma client instance
     */
    static getInstance(): PrismaClient {
        if (!this.instance) {
            this.instance = new PrismaClient();
        }

        return this.instance;
    }

    /**
     * Connects to the database
     */
    static async connect(): Promise<void> {
        if (this.connected) {
            logger.debug('Prisma already connected, skipping');
            return;
        }

        const prisma = this.getInstance();
        await prisma.$connect();
        this.connected = true;
        logger.info('✅ Database connected');
    }

    /**
     * Disconnects from the database
     */
    static async disconnect(): Promise<void> {
        if (this.instance && this.connected) {
            await this.instance.$disconnect();
            this.instance = null;
            this.connected = false;
            logger.info('👋 Database disconnected');
        }
    }

    /**
     * Tests the database connection
     */
    static async healthCheck(): Promise<boolean> {
        try {
            const prisma = this.getInstance();
            await prisma.$queryRaw`SELECT 1`;
            return true;
        } catch {
            return false;
        }
    }
}

// Export du service et du client Prisma
export { PrismaService }; 