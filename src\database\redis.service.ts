import Redis from 'ioredis';

import { logger } from '@lib/Logger';
import { getEnv } from '@config/environment';

class RedisService {
    private static instance: Redis | null = null;
    private static isEnabled: boolean = true;

    /**
     * Checks if Redis is enabled
     */
    static isRedisEnabled(): boolean {
        return this.isEnabled;
    }

    /**
     * Disables Redis
     */
    static disable(): void {
        this.isEnabled = false;
        logger.info('⚠️ Redis disabled');
    }

    /**
     * Gets the Redis client instance
     */
    static getInstance(): Redis | null {
        if (!this.isEnabled) {
            return null;
        }

        if (!this.instance) {
            const env = getEnv();
            this.instance = new Redis(env.REDIS_URL, {
                password: env.REDIS_PASSWORD,
                retryStrategy: (times) => {
                    if (times > 3) {
                        logger.warn('⚠️ Redis: Too many reconnection attempts, disabling Redis');
                        this.disable();
                        return null;
                    }
                    const delay = Math.min(times * 50, 2000);
                    return delay;
                },
                maxRetriesPerRequest: 3,
                lazyConnect: true,
            });

            // Event handling
            this.instance.on('connect', () => {
                logger.info('🔗 Redis: Connection established');
            });

            this.instance.on('error', (error) => {
                logger.error('❌ Redis: Connection error', error);
            });

            this.instance.on('close', () => {
                logger.info('🔌 Redis: Connection closed');
            });
        }

        return this.instance;
    }

    /**
     * Connects to Redis
     */
    static async connect(): Promise<void> {
        if (!this.isEnabled) {
            logger.info('⚠️ Redis disabled, skipping connection');
            return;
        }

        try {
            const redis = this.getInstance();
            if (redis) {
                await redis.ping();
                logger.info('✅ Redis connected');
            }
        } catch (error) {
            logger.warn('⚠️ Redis unavailable, disabling:', error);
            this.disable();
        }
    }

    /**
     * Disconnects from Redis
     */
    static async disconnect(): Promise<void> {
        if (this.instance) {
            await this.instance.quit();
            this.instance = null;
            logger.info('👋 Redis disconnected');
        }
    }

    /**
     * Gets a value from cache
     */
    static async get<T = any>(key: string): Promise<T | null> {
        if (!this.isEnabled) return null;

        try {
            const redis = this.getInstance();
            if (!redis) return null;

            const value = await redis.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            logger.warn('⚠️ Redis get error:', error);
            return null;
        }
    }

    /**
     * Sets a value in cache
     */
    static async set(key: string, value: any, ttl?: number): Promise<void> {
        if (!this.isEnabled) return;

        try {
            const redis = this.getInstance();
            if (!redis) return;

            const serialized = JSON.stringify(value);

            if (ttl) {
                await redis.setex(key, ttl, serialized);
            } else {
                await redis.set(key, serialized);
            }
        } catch (error) {
            logger.warn('⚠️ Redis set error:', error);
        }
    }

    /**
     * Deletes a value from cache
     */
    static async del(key: string): Promise<void> {
        if (!this.isEnabled) return;

        try {
            const redis = this.getInstance();
            if (!redis) return;

            await redis.del(key);
        } catch (error) {
            logger.warn('⚠️ Redis del error:', error);
        }
    }

    /**
     * Deletes all keys matching a pattern
     */
    static async delPattern(pattern: string): Promise<void> {
        if (!this.isEnabled) return;

        try {
            const redis = this.getInstance();
            if (!redis) return;

            const keys = await redis.keys(pattern);

            if (keys.length > 0) {
                await redis.del(...keys);
            }
        } catch (error) {
            logger.warn('⚠️ Redis delPattern error:', error);
        }
    }

    /**
     * Checks if a key exists
     */
    static async exists(key: string): Promise<boolean> {
        if (!this.isEnabled) return false;

        try {
            const redis = this.getInstance();
            if (!redis) return false;

            return (await redis.exists(key)) === 1;
        } catch (error) {
            logger.warn('⚠️ Redis exists error:', error);
            return false;
        }
    }

    /**
     * Increments a value
     */
    static async incr(key: string): Promise<number> {
        if (!this.isEnabled) return 0;

        try {
            const redis = this.getInstance();
            if (!redis) return 0;

            return await redis.incr(key);
        } catch (error) {
            logger.warn('⚠️ Redis incr error:', error);
            return 0;
        }
    }

    /**
     * Decrements a value
     */
    static async decr(key: string): Promise<number> {
        if (!this.isEnabled) return 0;

        try {
            const redis = this.getInstance();
            if (!redis) return 0;

            return await redis.decr(key);
        } catch (error) {
            logger.warn('⚠️ Redis decr error:', error);
            return 0;
        }
    }

    /**
     * Sets an expiration on a key
     */
    static async expire(key: string, seconds: number): Promise<void> {
        if (!this.isEnabled) return;

        try {
            const redis = this.getInstance();
            if (!redis) return;

            await redis.expire(key, seconds);
        } catch (error) {
            logger.warn('⚠️ Redis expire error:', error);
        }
    }

    /**
     * Tests the Redis connection
     */
    static async healthCheck(): Promise<boolean> {
        if (!this.isEnabled) return false;

        try {
            const redis = this.getInstance();
            if (!redis) return false;

            await redis.ping();
            return true;
        } catch {
            return false;
        }
    }
}

export { RedisService }; 