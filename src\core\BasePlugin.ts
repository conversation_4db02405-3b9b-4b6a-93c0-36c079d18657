import type { ExtendedClient } from '@/client';
import type { BaseCommand } from './BaseCommand';
import type { BaseEvent } from './BaseEvent';
import type { BaseInteraction } from './BaseInteraction';

export interface PluginInfo {
    name: string;
    version: string;
    description: string;
    author?: string;
    dependencies?: string[];
}

export abstract class BasePlugin {
    public abstract readonly info: PluginInfo;
    public readonly client: ExtendedClient;
    public readonly commands: Map<string, BaseCommand> = new Map();
    public readonly events: Map<string, BaseEvent<any>> = new Map();
    public readonly interactions: Map<string, BaseInteraction> = new Map();

    private _loaded = false;

    constructor(client: ExtendedClient) {
        this.client = client;
    }

    /**
     * Charge le plugin
     */
    async load(): Promise<void> {
        if (this._loaded) {
            throw new Error(`Le plugin ${this.info.name} est déjà chargé`);
        }

        // Vérifier les dépendances
        if (this.info.dependencies?.length) {
            for (const dep of this.info.dependencies) {
                if (!this.client.plugins.has(dep)) {
                    throw new Error(`Le plugin ${this.info.name} nécessite le plugin ${dep}`);
                }
            }
        }

        // Charger le plugin
        await this.onLoad();

        // Enregistrer les commandes
        for (const command of this.commands.values()) {
            this.client.commands.set(command.name, command);
        }

        // Enregistrer les événements
        for (const event of this.events.values()) {
            event.register();
        }

        this._loaded = true;
    }

    /**
     * Décharge le plugin
     */
    async unload(): Promise<void> {
        if (!this._loaded) {
            throw new Error(`Le plugin ${this.info.name} n'est pas chargé`);
        }

        // Décharger le plugin
        await this.onUnload();

        // Supprimer les commandes
        for (const command of this.commands.values()) {
            this.client.commands.delete(command.name);
        }

        // Supprimer les événements
        for (const event of this.events.values()) {
            event.unregister();
        }

        // Vider les collections
        this.commands.clear();
        this.events.clear();
        this.interactions.clear();

        this._loaded = false;
    }

    /**
     * Recharge le plugin
     */
    async reload(): Promise<void> {
        await this.unload();
        await this.load();
    }

    /**
     * Vérifie si le plugin est chargé
     */
    get isLoaded(): boolean {
        return this._loaded;
    }

    /**
     * Méthode appelée lors du chargement du plugin
     */
    protected abstract onLoad(): Promise<void> | void;

    /**
     * Méthode appelée lors du déchargement du plugin
     */
    protected abstract onUnload(): Promise<void> | void;

    /**
     * Ajoute une commande au plugin
     */
    protected addCommand(command: BaseCommand): void {
        this.commands.set(command.name, command);
    }

    /**
     * Ajoute un événement au plugin
     */
    protected addEvent(event: BaseEvent<any>): void {
        this.events.set(event.name, event);
    }

    /**
     * Ajoute une interaction au plugin
     */
    protected addInteraction(interaction: BaseInteraction): void {
        const key = typeof interaction.customId === 'string'
            ? interaction.customId
            : interaction.customId.source;
        this.interactions.set(key, interaction);
    }
} 