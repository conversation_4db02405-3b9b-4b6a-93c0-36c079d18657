/**
 * Mappe nos codes de langue vers les locales Discord.js
 */
export const LOCALE_MAP: Record<string, string> = {
    'en-US': 'en-US',
    'en-GB': 'en-GB',
    'fr-FR': 'fr',
    'es-ES': 'es-ES',
    'de-DE': 'de',
    'pt-BR': 'pt-BR',
    'ru-RU': 'ru',
    'ja-JP': 'ja',
    'ko-KR': 'ko',
    'zh-CN': 'zh-CN',
    'zh-TW': 'zh-TW',
};

/**
 * Convertit notre format de langue vers les locales Discord
 */
export function getDiscordLocale(lang: string): string | null {
    return LOCALE_MAP[lang] || null;
}

/**
 * Obtient toutes les locales Discord supportées
 */
export function getSupportedDiscordLocales(): string[] {
    return Object.values(LOCALE_MAP);
} 