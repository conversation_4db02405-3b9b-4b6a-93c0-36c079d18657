import { readdirSync } from 'fs';
import { join } from 'path';

import { logger } from '@lib/Logger';
import type { ExtendedClient } from '@/client';
import type { BasePlugin } from '@core/BasePlugin';

/**
 * Charge tous les modules/plugins
 */
export async function loadModules(client: ExtendedClient): Promise<void> {
    const modulesPath = join(__dirname);
    const moduleFiles = readdirSync(modulesPath, { withFileTypes: true });

    for (const file of moduleFiles) {
        if (!file.isDirectory()) {
            continue;
        }

        try {
            // Import the module (will use index.js or main file)
            const pluginModule = await import(`./${file.name}`);

            // Get the plugin class
            const PluginClass = pluginModule.default;

            if (typeof PluginClass !== 'function') {
                logger.warn(`⚠️ Module ${file.name} does not export a valid plugin class`);
                continue;
            }

            // Create and load the plugin
            const plugin: BasePlugin = new PluginClass(client);
            await plugin.load();

            client.plugins.set(plugin.info.name, plugin);
            logger.info(`✅ Module loaded: ${plugin.info.name} v${plugin.info.version}`);

        } catch (error) {
            logger.error(`❌ Error loading module ${file.name}:`, error);
        }
    }
} 