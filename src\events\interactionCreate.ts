import type { Interaction } from 'discord.js';

import { BaseEvent } from '@core/BaseEvent';
import { InteractionError } from '@core/InteractionError';
import { logger } from '@lib/Logger';
import { I18nService } from '@services/I18nService';
import type { ExtendedClient } from '@/client';

export class InteractionCreateEvent extends BaseEvent<'interactionCreate'> {
    public readonly name = 'interactionCreate' as const;
    public readonly once = false;

    private cooldowns = new Map<string, Map<string, number>>();

    constructor(client: ExtendedClient) {
        super(client);
    }

    async execute(interaction: Interaction): Promise<void> {
        try {
            if (interaction.isChatInputCommand()) {
                await this.handleCommand(interaction);
            } else if (interaction.isButton()) {
                await this.handleButton(interaction);
            } else if (interaction.isStringSelectMenu()) {
                await this.handleSelectMenu(interaction);
            } else if (interaction.isModalSubmit()) {
                await this.handleModal(interaction);
            }
        } catch (error) {
            logger.error('Error processing interaction:', error);
            await this.handleError(interaction, error);
        }
    }

    private async handleCommand(interaction: any): Promise<void> {
        const command = this.client.commands.get(interaction.commandName);

        if (!command) {
            const message = await I18nService.translate('errors:commands.notFound', {
                user: interaction.user,
                guild: interaction.guild || undefined,
            });

            await interaction.reply({
                content: message,
                ephemeral: true,
            });
            return;
        }

        // Vérifier les permissions
        if (!(await command.canExecute(interaction))) {
            return;
        }

        // Vérifier le cooldown seulement si il est > 0
        const cooldownAmount = command.options.cooldown || 0;
        if (cooldownAmount > 0 && !this.checkCooldown(interaction.user.id, command.name, cooldownAmount)) {
            const timeLeft = this.getCooldownTimeLeft(interaction.user.id, command.name);
            const message = await I18nService.translate('errors:commands.cooldown', {
                user: interaction.user,
                guild: interaction.guild || undefined,
            }, { seconds: timeLeft });

            await interaction.reply({
                content: message,
                ephemeral: true,
            });
            return;
        }

        // Exécuter la commande
        await command.execute(interaction);
    }

    private async handleButton(interaction: any): Promise<void> {
        // Chercher l'interaction dans tous les plugins
        for (const plugin of this.client.plugins.values()) {
            for (const handler of plugin.interactions.values()) {
                if (handler.matches(interaction.customId)) {
                    await handler.execute(interaction);
                    return;
                }
            }
        }

        const message = await I18nService.translate('errors:interaction.notAvailable', {
            user: interaction.user,
            guild: interaction.guild || undefined,
        });

        await interaction.reply({
            content: message,
            ephemeral: true,
        });
    }

    private async handleSelectMenu(interaction: any): Promise<void> {
        // Même logique que handleButton
        await this.handleButton(interaction);
    }

    private async handleModal(interaction: any): Promise<void> {
        // Même logique que handleButton
        await this.handleButton(interaction);
    }

    private async handleError(interaction: any, error: unknown): Promise<void> {
        let errorMessage: string;

        if (error instanceof InteractionError) {
            errorMessage = error.message;
        } else {
            errorMessage = await I18nService.translate('errors:general.unexpected', {
                user: interaction.user,
                guild: interaction.guild || undefined,
            });
        }

        const ephemeral = error instanceof InteractionError ? error.ephemeral : true;

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage,
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral,
                });
            }
        } catch {
            // Ignorer les erreurs lors de la réponse
        }
    }

    private checkCooldown(userId: string, commandName: string, cooldownAmount: number): boolean {
        if (!this.cooldowns.has(commandName)) {
            this.cooldowns.set(commandName, new Map());
        }

        const now = Date.now();
        const timestamps = this.cooldowns.get(commandName)!;
        const cooldownTime = cooldownAmount * 1000;

        if (timestamps.has(userId)) {
            const expirationTime = timestamps.get(userId)! + cooldownTime;

            if (now < expirationTime) {
                return false;
            }
        }

        timestamps.set(userId, now);
        setTimeout(() => timestamps.delete(userId), cooldownTime);

        return true;
    }

    private getCooldownTimeLeft(userId: string, commandName: string): number {
        const timestamps = this.cooldowns.get(commandName);
        if (!timestamps || !timestamps.has(userId)) return 0;

        const now = Date.now();
        const expirationTime = timestamps.get(userId)! + 3000; // 3 secondes par défaut
        return Math.ceil((expirationTime - now) / 1000);
    }
} 