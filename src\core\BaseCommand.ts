import type {
    ChatInputCommandInteraction,
    SlashCommandBuilder,
    SlashCommandOptionsOnlyBuilder,
    SlashCommandSubcommandsOnlyBuilder,
    Locale,
    PermissionsBitField,
} from 'discord.js';

import type { ExtendedClient } from '@/client';
import type { CategoryId } from '@config/categories';
import { I18nService } from '@services/I18nService';
import { getEnv } from '@config/environment';
import { getDiscordLocale } from '@utils/localeMapper';

export type CommandBuilder =
    | SlashCommandBuilder
    | SlashCommandOptionsOnlyBuilder
    | SlashCommandSubcommandsOnlyBuilder
    | Omit<SlashCommandBuilder, 'addSubcommand' | 'addSubcommandGroup'>;

export interface CommandOptions {
    cooldown?: number; // En secondes
    devOnly?: boolean;
    userPermissions?: bigint[];
    botPermissions?: bigint[];
    category: CategoryId;
    nameKey: string; // Clé de traduction pour le nom
    descriptionKey: string; // Clé de traduction pour la description
}

export abstract class BaseCommand {
    public readonly options: CommandOptions;
    public readonly client: ExtendedClient;

    constructor(client: ExtendedClient, options: CommandOptions) {
        this.client = client;
        this.options = {
            cooldown: 0,
            devOnly: false,
            userPermissions: [],
            botPermissions: [],
            ...options,
        };
    }

    /**
     * Obtient le nom de la commande traduit
     */
    get name(): string {
        // Pour les commandes, on utilise la version anglaise par défaut comme identifiant
        return I18nService.t(this.options.nameKey, {}, 'en-US').toLowerCase().replace(/\s+/g, '-');
    }

    /**
     * Obtient la description de la commande traduite
     */
    get description(): string {
        return I18nService.t(this.options.descriptionKey, {}, 'en-US');
    }

    /**
     * Construit la commande slash de base (à implémenter par les sous-classes)
     * Les sous-classes n'ont qu'à retourner un builder avec les options,
     * les traductions seront appliquées automatiquement
     */
    abstract buildCommand(): CommandBuilder;

    /**
     * Applique automatiquement les traductions au builder
     */
    protected applyLocalizations(builder: CommandBuilder): CommandBuilder {
        // Cast vers SlashCommandBuilder pour accéder aux méthodes de localisation
        const slashBuilder = builder as SlashCommandBuilder;

        // Définir le nom et description de base (en-US)
        slashBuilder
            .setName(this.name)
            .setDescription(this.description);

        // Ajouter les traductions pour chaque langue supportée
        const languages = I18nService.getSupportedLanguages();

        languages.forEach(lang => {
            if (lang !== 'en-US') {
                const locale = getDiscordLocale(lang);
                if (locale) {
                    // Traduire le nom et la description
                    const translatedName = I18nService.t(this.options.nameKey, {}, lang);
                    const translatedDescription = I18nService.t(this.options.descriptionKey, {}, lang);

                    slashBuilder
                        .setNameLocalization(locale as Locale, translatedName)
                        .setDescriptionLocalization(locale as Locale, translatedDescription);
                }
            }
        });

        return builder;
    }

    /**
     * Traduit automatiquement une option (string, integer, etc.)
     */
    protected translateOption(
        option: any,
        nameKey: string,
        descriptionKey: string,
    ): any {
        // Définir le nom et description de base (en-US)
        option
            .setName(I18nService.t(nameKey, {}, 'en-US'))
            .setDescription(I18nService.t(descriptionKey, {}, 'en-US'));

        // Ajouter les traductions
        const languages = I18nService.getSupportedLanguages();

        languages.forEach(lang => {
            if (lang !== 'en-US') {
                const locale = getDiscordLocale(lang);
                if (locale) {
                    option
                        .setNameLocalization(locale as Locale, I18nService.t(nameKey, {}, lang))
                        .setDescriptionLocalization(locale as Locale, I18nService.t(descriptionKey, {}, lang));
                }
            }
        });

        return option;
    }

    /**
     * Traduit automatiquement un choix d'option
     */
    protected translateChoice(
        choice: any,
        nameKey: string,
        value: string,
    ): any {
        // Définir le nom de base (en-US)
        choice.setName(I18nService.t(nameKey, {}, 'en-US'));
        choice.setValue(value);

        // Ajouter les traductions
        const languages = I18nService.getSupportedLanguages();

        languages.forEach(lang => {
            if (lang !== 'en-US') {
                const locale = getDiscordLocale(lang);
                if (locale) {
                    choice.setNameLocalization(locale as Locale, I18nService.t(nameKey, {}, lang));
                }
            }
        });

        return choice;
    }

    /**
     * Les données de la commande slash avec traductions automatiques
     */
    get data(): CommandBuilder {
        const builder = this.buildCommand();
        return this.applyLocalizations(builder);
    }

    /**
     * Exécute la commande
     */
    abstract execute(interaction: ChatInputCommandInteraction): Promise<void>;

    /**
     * Vérifie si l'utilisateur peut exécuter la commande
     */
    async canExecute(interaction: ChatInputCommandInteraction): Promise<boolean> {
        // Vérifier si c'est une commande dev-only
        if (this.options.devOnly) {
            const ownerId = getEnv().BOT_OWNER_ID;
            if (!ownerId || interaction.user.id !== ownerId) {
                await interaction.reply({
                    content: await I18nService.translate('errors:commands.devOnly', {
                        user: interaction.user,
                        guild: interaction.guild || undefined,
                    }),
                    ephemeral: true,
                });
                return false;
            }
        }

        // Vérifier les permissions utilisateur
        if (this.options.userPermissions?.length && interaction.inGuild()) {
            const member = interaction.member;
            if (!member) return false;

            // Vérifier que les permissions sont bien un PermissionsBitField
            if (typeof member.permissions === 'string') return false;

            const permissions = member.permissions as PermissionsBitField;
            const missingPerms = this.options.userPermissions.filter(
                (perm) => !permissions.has(perm),
            );

            if (missingPerms.length > 0) {
                await interaction.reply({
                    content: await I18nService.translate('errors:commands.missingUserPermissions', {
                        user: interaction.user,
                        guild: interaction.guild || undefined,
                    }),
                    ephemeral: true,
                });
                return false;
            }
        }

        // Vérifier les permissions du bot
        if (this.options.botPermissions?.length && interaction.inGuild()) {
            const botMember = interaction.guild?.members.me;
            if (!botMember) return false;

            const missingPerms = this.options.botPermissions.filter(
                (perm) => !botMember.permissions.has(perm),
            );

            if (missingPerms.length > 0) {
                await interaction.reply({
                    content: await I18nService.translate('errors:commands.missingBotPermissions', {
                        user: interaction.user,
                        guild: interaction.guild || undefined,
                    }),
                    ephemeral: true,
                });
                return false;
            }
        }

        return true;
    }

    /**
     * Méthode utilitaire pour répondre avec une erreur
     */
    protected async replyError(
        interaction: ChatInputCommandInteraction,
        message: string,
    ): Promise<void> {
        // Le message est déjà traduit, on l'utilise tel quel
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({
                content: message,
            });
        } else {
            await interaction.reply({
                content: message,
                ephemeral: true,
            });
        }
    }

    /**
     * Méthode utilitaire pour répondre avec un succès
     */
    protected async replySuccess(
        interaction: ChatInputCommandInteraction,
        message: string,
    ): Promise<void> {
        // Le message est déjà traduit, on l'utilise tel quel
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({
                content: message,
            });
        } else {
            await interaction.reply({
                content: message,
                ephemeral: false,
            });
        }
    }

    /**
     * Méthode utilitaire pour traduire et répondre
     */
    protected async translate(
        key: string,
        interaction: ChatInputCommandInteraction,
        options?: any,
    ): Promise<string> {
        return I18nService.translate(key, {
            user: interaction.user,
            guild: interaction.guild || undefined,
        }, options);
    }
} 