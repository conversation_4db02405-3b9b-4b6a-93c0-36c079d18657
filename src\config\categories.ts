export interface CommandCategory {
    id: string;
    nameKey: string; // Clé de traduction pour le nom
    descriptionKey: string; // Clé de traduction pour la description
    emoji?: string;
    order: number; // Pour trier les catégories
}

export const CommandCategories: Record<string, CommandCategory> = {
    GENERAL: {
        id: 'general',
        nameKey: 'categories:general.name',
        descriptionKey: 'categories:general.description',
        emoji: '📌',
        order: 1,
    },
    ADMIN: {
        id: 'admin',
        nameKey: 'categories:admin.name',
        descriptionKey: 'categories:admin.description',
        emoji: '⚙️',
        order: 9,
    },
} as const;

export type CategoryId = keyof typeof CommandCategories; 