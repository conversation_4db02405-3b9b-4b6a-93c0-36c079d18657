import { Slash<PERSON>ommandBuilder, EmbedBuilder, type ChatInputCommandInteraction } from 'discord.js';
import { BaseCommand } from '@core/BaseCommand';
import { ClusterService } from '@services/ClusterService';
import type { ExtendedClient } from '@/client';

export class ClusterStatsCommand extends BaseCommand {
    constructor(client: ExtendedClient) {
        super(client, {
            category: 'ADMIN',
            nameKey: 'commands:clusterstats.name',
            descriptionKey: 'commands:clusterstats.description',
            cooldown: 10,
            userPermissions: [8n], // Administrator permission
        });
    }

    buildCommand(): SlashCommandBuilder {
        return new SlashCommandBuilder()
            .setDefaultMemberPermissions('0'); // Admin only
    }

    async execute(interaction: ChatInputCommandInteraction): Promise<void> {
        await interaction.deferReply({ ephemeral: true });

        try {
            const clusterService = ClusterService.getInstance();

            if (!clusterService.isClustered()) {
                const embed = new EmbedBuilder()
                    .setTitle('📊 Statistiques du Bot')
                    .setDescription('Le bot fonctionne en mode processus unique (pas de clustering)')
                    .addFields(
                        {
                            name: '🏓 Ping',
                            value: `${this.client.ws.ping}ms`,
                            inline: true,
                        },
                        {
                            name: '🏠 Serveurs',
                            value: this.client.guilds.cache.size.toString(),
                            inline: true,
                        },
                        {
                            name: '👥 Utilisateurs',
                            value: this.client.users.cache.size.toString(),
                            inline: true,
                        },
                        {
                            name: '⏱️ Uptime',
                            value: this.formatUptime(process.uptime()),
                            inline: true,
                        },
                        {
                            name: '💾 Mémoire',
                            value: this.formatMemory(process.memoryUsage().heapUsed),
                            inline: true,
                        }
                    )
                    .setColor(0x00ff00)
                    .setTimestamp();

                await interaction.editReply({ embeds: [embed] });
                return;
            }

            // Get cluster information
            const clusterInfo = clusterService.getClusterInfo();
            const clusterStats = await clusterService.getClusterStats();
            const totalGuilds = await clusterService.getTotalGuildCount();
            const totalUsers = await clusterService.getTotalUserCount();

            const embed = new EmbedBuilder()
                .setTitle('🔷 Statistiques des Clusters')
                .setDescription(`Cluster actuel: **${clusterInfo.id}** | Total clusters: **${clusterInfo.totalClusters}**`)
                .addFields(
                    {
                        name: '🏠 Total Serveurs',
                        value: totalGuilds.toString(),
                        inline: true,
                    },
                    {
                        name: '👥 Total Utilisateurs',
                        value: totalUsers.toString(),
                        inline: true,
                    },
                    {
                        name: '🔢 Total Shards',
                        value: clusterInfo.totalShards.toString(),
                        inline: true,
                    }
                )
                .setColor(0x0099ff)
                .setTimestamp();

            // Add individual cluster stats
            let clusterStatsText = '';
            for (const stats of clusterStats) {
                const isCurrentCluster = stats.clusterId === clusterInfo.id;
                const indicator = isCurrentCluster ? '🟢' : '🔵';

                clusterStatsText += `${indicator} **Cluster ${stats.clusterId}**\n`;
                clusterStatsText += `├ Ping: ${stats.ping}ms\n`;
                clusterStatsText += `├ Serveurs: ${stats.guilds}\n`;
                clusterStatsText += `├ Utilisateurs: ${stats.users}\n`;
                clusterStatsText += `├ Uptime: ${this.formatUptime(stats.uptime)}\n`;
                clusterStatsText += `└ Mémoire: ${this.formatMemory(stats.memoryUsage.heapUsed)}\n\n`;
            }

            if (clusterStatsText.length > 1024) {
                clusterStatsText = clusterStatsText.substring(0, 1021) + '...';
            }

            embed.addFields({
                name: '📈 Détails des Clusters',
                value: clusterStatsText || 'Aucune donnée disponible',
                inline: false,
            });

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in cluster stats command:', error);

            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Erreur')
                .setDescription('Une erreur est survenue lors de la récupération des statistiques des clusters.')
                .setColor(0xff0000)
                .setTimestamp();

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }

    private formatUptime(seconds: number): string {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) {
            return `${days}j ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }

    private formatMemory(bytes: number): string {
        const mb = bytes / 1024 / 1024;
        return `${mb.toFixed(1)} MB`;
    }
} 