require('module-alias/register');
import 'dotenv/config';
import { validateEnvironment, getEnv } from '@config/environment';

// Valider l'environnement dès que possible
validateEnvironment();

import { ShardingManager } from 'discord.js';
import { resolve } from 'path';

import { logger } from '@lib/Logger';

async function bootstrap() {
    try {
        // Get validated environment
        const env = getEnv();

        // Déterminer le mode de démarrage
        if (env.USE_CLUSTERING) {
            // Lancer le gestionnaire de clusters (discord-hybrid-sharding)
            logger.info('🚀 Starting with clustering mode (discord-hybrid-sharding)');
            require('./cluster');
        } else if (env.USE_SHARDING) {
            // Lancer le gestionnaire de shards (discord.js natif)
            logger.info('🚀 Starting with sharding mode (discord.js native)');
            const manager = new ShardingManager(resolve(__dirname, 'bot.js'), {
                token: env.DISCORD_TOKEN,
                totalShards: 'auto',
            });

            manager.on('shardCreate', (shard) => {
                logger.info(`🔷 Shard ${shard.id} lancé`);
            });

            await manager.spawn();
            logger.info('✅ Tous les shards ont été lancés');
        } else {
            // Lancer le bot directement sans sharding ni clustering
            logger.info('🚀 Starting in single process mode');
            require('./bot');
        }
    } catch (error) {
        logger.error('❌ Erreur fatale lors du démarrage:', error);
        process.exit(1);
    }
}

// Gestion des erreurs non capturées
process.on('unhandledRejection', (error: Error) => {
    logger.error('Unhandled Rejection:', error);
});

process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Démarrer l'application
bootstrap(); 